"use client";

let isInitialized = false;

/**
 * Check if we're running in a Telegram environment before trying to use the SDK
 */
function isTelegramEnvironment(): boolean {
  if (typeof window === "undefined") {
    return false;
  }

  // Check for Telegram WebApp object
  if (window.Telegram?.WebApp) {
    return true;
  }

  // Check for tgWebAppPlatform in URL or other Telegram-specific parameters
  const url = new URL(window.location.href);
  const hasWebAppPlatform =
    url.searchParams.has("tgWebAppPlatform") ||
    url.searchParams.has("tgWebAppData") ||
    url.searchParams.has("initData");

  if (hasWebAppPlatform) {
    return true;
  }

  // Check localStorage for Telegram data
  try {
    const hasStorageData =
      localStorage.getItem("telegram-apps/launch-params") !== null;
    if (hasStorageData) {
      return true;
    }
  } catch {
    // Ignore localStorage errors
  }

  return false;
}

/**
 * Client-side only Telegram SDK initialization
 * This prevents hydration errors by only running on the client
 */
export function initTelegramSDK(): boolean {
  if (typeof window === "undefined" || isInitialized) {
    return isInitialized;
  }

  // Only try to initialize if we're in a Telegram environment
  if (!isTelegramEnvironment()) {
    console.log("Not in Telegram environment, skipping SDK initialization");
    return false;
  }

  try {
    console.log("Initializing Telegram SDK on client...");

    // Dynamically import and initialize the SDK only when needed
    import("@telegram-apps/sdk")
      .then(({ init, miniApp, themeParams, viewport }) => {
        try {
          // Initialize the SDK
          init();

          // Initialize mini app
          try {
            miniApp.mount();
            miniApp.ready();
          } catch (error) {
            console.warn("Failed to mount miniApp:", error);
          }

          // Set up viewport
          try {
            viewport.mount();
            if (viewport.expand.isAvailable()) {
              viewport.expand();
            }
          } catch (error) {
            console.warn("Failed to mount viewport:", error);
          }

          // Set theme colors if available
          try {
            themeParams.mount();
          } catch (error) {
            console.warn("Failed to mount themeParams:", error);
          }

          isInitialized = true;
          console.log("Telegram SDK initialized successfully");
        } catch (error) {
          console.error("Failed to initialize Telegram SDK:", error);
        }
      })
      .catch((error) => {
        console.error("Failed to load Telegram SDK:", error);
      });

    return true;
  } catch (error) {
    console.error("Failed to initialize Telegram SDK:", error);
    return false;
  }
}

/**
 * Client-side only function to get launch params
 * This prevents hydration errors and handles cases where app is opened outside Telegram
 */
export function getTelegramLaunchParams() {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    return retrieveLaunchParams();
  } catch (error) {
    // This is expected when app is opened outside Telegram
    console.log(
      "App is not running in Telegram environment:",
      error instanceof Error ? error.message : String(error)
    );
    return null;
  }
}

/**
 * Client-side only function to get Telegram init data
 * Uses tgWebAppData as primary source, falls back to initDataRaw
 */
export function getTelegramInitData(): string {
  if (typeof window === "undefined") {
    return "";
  }

  try {
    const launchParams = getTelegramLaunchParams();

    // First try tgWebAppData if available
    if (
      launchParams &&
      typeof launchParams === "object" &&
      "tgWebAppData" in launchParams &&
      launchParams.tgWebAppData
    ) {
      return String(launchParams.tgWebAppData);
    }

    // Fallback to initDataRaw
    if (
      launchParams &&
      typeof launchParams === "object" &&
      "initDataRaw" in launchParams &&
      launchParams.initDataRaw
    ) {
      return String(launchParams.initDataRaw);
    }

    // Final fallback to window.Telegram.WebApp
    const telegramWebApp = window.Telegram?.WebApp;
    if (telegramWebApp?.initData) {
      return telegramWebApp.initData;
    }

    return "";
  } catch (error) {
    console.warn("Failed to get Telegram init data:", error);
    return "";
  }
}
