"use client";

import { retrieveLaunchParams, init, miniApp, themeParams, viewport } from "@telegram-apps/sdk";

let isInitialized = false;

/**
 * Client-side only Telegram SDK initialization
 * This prevents hydration errors by only running on the client
 */
export function initTelegramSDK(): boolean {
  if (typeof window === "undefined" || isInitialized) {
    return isInitialized;
  }

  try {
    console.log("Initializing Telegram SDK on client...");
    
    // Initialize the SDK
    init();
    
    // Initialize mini app
    try {
      miniApp.mount();
      miniApp.ready();
    } catch (error) {
      console.warn("Failed to mount miniApp:", error);
    }

    // Set up viewport
    try {
      viewport.mount();
      if (viewport.expand.isAvailable()) {
        viewport.expand();
      }
    } catch (error) {
      console.warn("Failed to mount viewport:", error);
    }

    // Set theme colors if available
    try {
      themeParams.mount();
    } catch (error) {
      console.warn("Failed to mount themeParams:", error);
    }

    isInitialized = true;
    console.log("Telegram SDK initialized successfully");
    return true;
  } catch (error) {
    console.error("Failed to initialize Telegram SDK:", error);
    return false;
  }
}

/**
 * Client-side only function to get launch params
 * This prevents hydration errors
 */
export function getTelegramLaunchParams() {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    return retrieveLaunchParams();
  } catch (error) {
    console.warn("Failed to get launch params:", error);
    return null;
  }
}

/**
 * Client-side only function to get Telegram init data
 * Uses tgWebAppData as primary source, falls back to initDataRaw
 */
export function getTelegramInitData(): string {
  if (typeof window === "undefined") {
    return "";
  }

  try {
    const launchParams = getTelegramLaunchParams();
    
    // First try tgWebAppData if available
    if (
      launchParams &&
      typeof launchParams === "object" &&
      "tgWebAppData" in launchParams &&
      launchParams.tgWebAppData
    ) {
      return String(launchParams.tgWebAppData);
    }
    
    // Fallback to initDataRaw
    if (
      launchParams &&
      typeof launchParams === "object" &&
      "initDataRaw" in launchParams &&
      launchParams.initDataRaw
    ) {
      return String(launchParams.initDataRaw);
    }
    
    // Final fallback to window.Telegram.WebApp
    const telegramWebApp = window.Telegram?.WebApp;
    if (telegramWebApp?.initData) {
      return telegramWebApp.initData;
    }
    
    return "";
  } catch (error) {
    console.warn("Failed to get Telegram init data:", error);
    return "";
  }
}
