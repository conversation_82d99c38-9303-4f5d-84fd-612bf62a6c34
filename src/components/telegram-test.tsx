"use client";

import { useEffect, useState } from "react";
import { useRootContext } from "@/root-context";
import { useTelegramWebApp } from "@/hooks/useTelegramWebApp";
import { retrieveLaunchParams } from "@telegram-apps/sdk";
import WebApp from "@twa-dev/sdk";

export const TelegramTest = () => {
  const { currentUser } = useRootContext();
  const { isInTelegram, isInitialized, telegramData, isLoading, error, retry } =
    useTelegramWebApp();
  const [scriptLoaded, setScriptLoaded] = useState(false);

  useEffect(() => {
    // Check if Telegram script is loaded
    const checkTelegramScript = () => {
      if (typeof window !== "undefined" && window.Telegram?.WebApp) {
        setScriptLoaded(true);
      } else {
        // Wait a bit and check again
        setTimeout(checkTelegramScript, 100);
      }
    };

    checkTelegramScript();
  }, []);

  return (
    <div className="p-6 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Telegram Integration Test</h3>

      <div className="space-y-4">
        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
            <span className="text-blue-800">
              Initializing Telegram Web App...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 font-medium">❌ Error</p>
            <p className="text-red-700 text-sm mt-1">{error}</p>
            <button
              onClick={retry}
              className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        )}

        {/* Status Information */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <strong>Is in Telegram Web App:</strong>{" "}
            <span className={isInTelegram ? "text-green-600" : "text-red-600"}>
              {isInTelegram ? "Yes" : "No"}
            </span>
          </div>
          <div>
            <strong>Is Initialized:</strong>{" "}
            <span
              className={isInitialized ? "text-green-600" : "text-orange-600"}
            >
              {isInitialized ? "Yes" : "No"}
            </span>
          </div>
        </div>

        <div>
          <strong>Current Firebase User:</strong>{" "}
          {currentUser ? currentUser.uid : "Not signed in"}
        </div>

        {/* Telegram Data */}
        {telegramData && (
          <div>
            <strong>Telegram Data:</strong>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
              {JSON.stringify(telegramData, null, 2)}
            </pre>
          </div>
        )}

        {/* Telegram WebApp SDK Data */}
        {typeof window !== "undefined" && (
          <div>
            <strong>Telegram WebApp SDK Data:</strong>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
              {JSON.stringify(
                {
                  telegramData: telegramData,
                  isInTelegram: isInTelegram,
                  isInitialized: isInitialized,
                },
                null,
                2
              )}
            </pre>
          </div>
        )}

        {/* SDK Debugging Information */}
        {typeof window !== "undefined" && (
          <div>
            <strong>SDK Debugging Information:</strong>
            <div className="mt-2 space-y-2">
              {/* Script Loading Status */}
              <div
                className={`p-2 rounded text-sm border ${
                  scriptLoaded
                    ? "bg-green-50 border-green-200"
                    : "bg-red-50 border-red-200"
                }`}
              >
                <strong>📜 Telegram Script Status:</strong>
                <pre className="mt-1 text-xs overflow-auto">
                  {JSON.stringify(
                    {
                      scriptLoaded: scriptLoaded,
                      scriptUrl: "https://telegram.org/js/telegram-web-app.js",
                      status: scriptLoaded
                        ? "✅ Loaded"
                        : "❌ Not loaded or still loading",
                    },
                    null,
                    2
                  )}
                </pre>
              </div>

              {/* @telegram-apps/sdk Data */}
              <div className="p-2 bg-green-50 rounded text-sm border border-green-200">
                <strong>🎯 @telegram-apps/sdk (Primary):</strong>
                <pre className="mt-1 text-xs overflow-auto">
                  {(() => {
                    try {
                      const launchParams = retrieveLaunchParams();
                      return JSON.stringify(
                        {
                          launchParams: launchParams,
                          initDataRaw: launchParams?.initDataRaw || "EMPTY",
                          initData: launchParams?.initData || "EMPTY",
                          platform: launchParams?.platform || "EMPTY",
                          version: launchParams?.version || "EMPTY",
                        },
                        null,
                        2
                      );
                    } catch (error) {
                      return `Error: ${
                        error instanceof Error ? error.message : "Unknown error"
                      }`;
                    }
                  })()}
                </pre>
              </div>

              {/* Global Telegram Object Check */}
              <div className="p-2 bg-yellow-50 rounded text-sm border border-yellow-200">
                <strong>🔍 Global Telegram Object Check:</strong>
                <pre className="mt-1 text-xs overflow-auto">
                  {(() => {
                    try {
                      const telegram = (window).Telegram;
                      return JSON.stringify(
                        {
                          telegramExists: !!telegram,
                          telegramKeys: telegram ? Object.keys(telegram) : [],
                          webAppExists: !!telegram?.WebApp,
                          webAppKeys: telegram?.WebApp
                            ? Object.keys(telegram.WebApp)
                            : [],
                        },
                        null,
                        2
                      );
                    } catch (error) {
                      return `Error: ${
                        error instanceof Error ? error.message : "Unknown error"
                      }`;
                    }
                  })()}
                </pre>
              </div>

              <div className="p-2 bg-blue-50 rounded text-sm border border-blue-200">
                <strong>📱 window.Telegram.WebApp (Fallback):</strong>
                <pre className="mt-1 text-xs overflow-auto">
                  {(() => {
                    try {
                      const telegramWebApp = window.Telegram?.WebApp;
                      if (telegramWebApp) {
                        return JSON.stringify(
                          {
                            initData: telegramWebApp.initData || "EMPTY",
                            initDataUnsafe:
                              telegramWebApp.initDataUnsafe || "EMPTY",
                            platform: telegramWebApp.platform || "EMPTY",
                            version: telegramWebApp.version || "EMPTY",
                            isExpanded: telegramWebApp.isExpanded,
                            viewportHeight: telegramWebApp.viewportHeight,
                            viewportStableHeight:
                              telegramWebApp.viewportStableHeight,
                            colorScheme: telegramWebApp.colorScheme,
                            themeParams: telegramWebApp.themeParams,
                          },
                          null,
                          2
                        );
                      } else {
                        return "window.Telegram.WebApp not found - script may not be loaded";
                      }
                    } catch (error) {
                      return `Error: ${
                        error instanceof Error ? error.message : "Unknown error"
                      }`;
                    }
                  })()}
                </pre>
              </div>
              <div className="p-2 bg-gray-100 rounded text-sm">
                <strong>📦 @twa-dev/sdk (Final Fallback):</strong>
                <pre className="mt-1 text-xs overflow-auto">
                  {JSON.stringify(
                    {
                      initData: WebApp.initData,
                      initDataUnsafe: WebApp.initDataUnsafe,
                      platform: WebApp.platform,
                      version: WebApp.version,
                    },
                    null,
                    2
                  )}
                </pre>
              </div>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p>
            <strong>Note:</strong> This component shows debug information about
            Telegram Web App integration. It will only show meaningful data when
            opened from within Telegram.
          </p>
          <div className="mt-4 flex gap-2">
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              🔄 Refresh Page
            </button>
            <button
              onClick={() => {
                setScriptLoaded(false);
                setTimeout(() => {
                  const checkTelegramScript = () => {
                    if (
                      typeof window !== "undefined" &&
                      window.Telegram?.WebApp
                    ) {
                      setScriptLoaded(true);
                    } else {
                      setTimeout(checkTelegramScript, 100);
                    }
                  };
                  checkTelegramScript();
                }, 100);
              }}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              🔍 Re-check Script
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TelegramTest;
