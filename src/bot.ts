import { Telegraf, <PERSON><PERSON> } from "telegraf";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;
const WEB_APP_URL =
  process.env.WEB_APP_URL ?? "https://4d5rqhd0-3000.euw.devtunnels.ms/";

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

// Create bot instance
const bot = new Telegraf(BOT_TOKEN);

// Set up menu button with custom URL
bot.telegram
  .setChatMenuButton({
    menuButton: {
      type: "web_app",
      text: "Open Marketplace",
      web_app: {
        url: WEB_APP_URL,
      },
    },
  })
  .catch(console.error);

// Start command handler
bot.start((ctx) => {
  const welcomeMessage = `
🛍️ Welcome to the Marketplace Bot!

This bot helps you access our marketplace platform. Use the buttons below to get started or open the full marketplace using the menu button.
  `;

  ctx.reply(
    welcomeMessage,
    Markup.keyboard([
      [Markup.button.text("👋 Hello World")],
      [Markup.button.text("✅ Complete Order")],
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ]).resize()
  );
});

// Hello World button handler
bot.hears("👋 Hello World", (ctx) => {
  ctx.reply(
    "👋 Hello World! Welcome to our marketplace bot.\n\n" +
      "This is a simple greeting message. You can use this bot to:\n" +
      "• Access the marketplace\n" +
      "• Complete orders\n" +
      "• Get support\n\n" +
      "Use the menu button above to open the full marketplace experience!",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ])
  );
});

// Complete Order button handler
bot.hears("✅ Complete Order", (ctx) => {
  ctx.reply(
    "📦 Complete Order\n\n" +
      "To complete an order, please use the marketplace web app where you can:\n" +
      "• View your active orders\n" +
      "• Track order status\n" +
      "• Complete payment\n" +
      "• Leave feedback\n\n" +
      "Click the button below to open the marketplace:",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
      [Markup.button.callback("📋 Order Help", "order_help")],
    ])
  );
});

// Order help callback handler
bot.action("order_help", (ctx) => {
  ctx.answerCbQuery();
  ctx.reply(
    "📋 Order Help\n\n" +
      "If you need help with your order:\n\n" +
      "1. Open the marketplace using the menu button\n" +
      "2. Go to your profile/orders section\n" +
      "3. Find your order and follow the instructions\n" +
      "4. Contact support if you encounter issues\n\n" +
      "For immediate assistance, you can also contact our support team.",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
      [Markup.button.callback("📞 Contact Support", "contact_support")],
    ])
  );
});

// Contact support callback handler
bot.action("contact_support", (ctx) => {
  ctx.answerCbQuery();
  ctx.reply(
    "📞 Contact Support\n\n" +
      "You can reach our support team through:\n\n" +
      "• Email: <EMAIL>\n" +
      "• Telegram: @marketplace_support\n" +
      "• Live chat in the web app\n\n" +
      "We typically respond within 24 hours.",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ])
  );
});

// Help command
bot.help((ctx) => {
  ctx.reply(
    "🤖 Marketplace Bot Help\n\n" +
      "Available commands:\n" +
      "/start - Start the bot and show main menu\n" +
      "/help - Show this help message\n\n" +
      "Available buttons:\n" +
      "👋 Hello World - Get a welcome message\n" +
      "✅ Complete Order - Get help with completing orders\n\n" +
      "You can also use the menu button to open the full marketplace web app.",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ])
  );
});

// Error handling
bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx.reply("Sorry, something went wrong. Please try again later.");
});

export default bot;
